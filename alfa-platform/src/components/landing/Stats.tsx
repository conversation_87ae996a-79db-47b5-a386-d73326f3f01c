'use client'

const stats = [
  {
    number: '250M+',
    description: 'API requests per day, peaking at 13,000 requests a second',
    icon: '📈'
  },
  {
    number: '99.999%',
    description: 'historical uptime for Stripe services',
    icon: '⚡'
  },
  {
    number: '90%',
    description: 'of US and all Fortune 500 companies using Stripe',
    icon: '🏢'
  },
  {
    number: '135+',
    description: 'currencies and payment methods supported',
    icon: '💳'
  }
]

export default function Stats() {
  return (
    <section className="py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute top-20 left-20 w-64 h-64 bg-purple-500 rounded-full blur-3xl"></div>
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-pink-500 rounded-full blur-3xl"></div>
      </div>
      
      <div className="container-alfa relative z-10">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-white mb-6 leading-tight">
            Elegí,
            <br />
            <span className="text-orange-300">Financiá.</span>
            <br />
            <span className="text-pink-300">Disfrutá</span>
          </h2>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            En tan solo 3 pasos...
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {stats.map((stat, index) => (
            <div key={index} className="text-center text-white">
              <div className="text-4xl mb-4">{stat.icon}</div>
              <div className="text-4xl md:text-5xl font-bold mb-4 bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent">
                {stat.number}
              </div>
              <p className="text-white/80 text-sm leading-relaxed">
                {stat.description}
              </p>
            </div>
          ))}
        </div>

        {/* Process Steps */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div className="text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-purple-500 to-pink-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">🛍️</span>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">1. Elegí</h3>
            <p className="text-white/80">
              Selecciona lo que quieres comprar en cualquiera de nuestros comercios aliados
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">💳</span>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">2. Financiá</h3>
            <p className="text-white/80">
              Paga en 4 cuotas mensuales sin interés, sin comisiones ocultas
            </p>
          </div>
          
          <div className="text-center">
            <div className="w-20 h-20 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
              <span className="text-2xl">🎉</span>
            </div>
            <h3 className="text-2xl font-bold text-white mb-4">3. Disfrutá</h3>
            <p className="text-white/80">
              Recibe tu producto inmediatamente y disfruta pagando cómodamente
            </p>
          </div>
        </div>

        {/* Decorative Elements */}
        <div className="relative mt-16">
          <div className="absolute inset-0 flex items-center justify-center opacity-5">
            <div className="w-96 h-96 border border-white/20 rounded-full"></div>
          </div>
          <div className="absolute inset-0 flex items-center justify-center opacity-10">
            <div className="w-64 h-64 border border-white/20 rounded-full"></div>
          </div>
        </div>
      </div>
    </section>
  )
} 