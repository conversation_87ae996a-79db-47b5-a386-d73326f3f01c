'use client'

import Link from 'next/link'

const footerLinks = {
  products: [
    { name: 'Pagos', href: '#' },
    { name: 'Billing', href: '#' },
    { name: 'Checkout', href: '#' },
    { name: 'Connect', href: '#' },
    { name: 'Radar', href: '#' },
    { name: 'Revenue Recognition', href: '#' }
  ],
  solutions: [
    { name: 'Startups', href: '#' },
    { name: 'Enterprises', href: '#' },
    { name: 'SaaS', href: '#' },
    { name: 'Platforms', href: '#' },
    { name: 'Ecommerce', href: '#' },
    { name: 'Marketplaces', href: '#' },
    { name: 'Crypto', href: '#' },
    { name: 'Creator Economy', href: '#' },
    { name: 'Embedded Finance', href: '#' }
  ],
  developers: [
    { name: 'Documentation', href: '#' },
    { name: 'API Reference', href: '#' },
    { name: 'API Status', href: '#' },
    { name: 'API Changelog', href: '#' },
    { name: 'Build a Stripe App', href: '#' }
  ],
  resources: [
    { name: 'Guides', href: '#' },
    { name: 'Customer Stories', href: '#' },
    { name: 'Blog', href: '#' },
    { name: 'Annual Conference', href: '#' },
    { name: 'Privacy & Terms', href: '#' },
    { name: 'Prohibited & Restricted Businesses', href: '#' },
    { name: 'Licenses', href: '#' }
  ],
  company: [
    { name: 'Jobs', href: '#' },
    { name: 'Newsroom', href: '#' },
    { name: 'Stripe Press', href: '#' },
    { name: 'Become a Partner', href: '#' }
  ],
  support: [
    { name: 'Contact Sales', href: '#' },
    { name: 'Support Center', href: '#' },
    { name: 'Support Plans', href: '#' },
    { name: '****** 976 2289', href: 'tel:+***********' }
  ]
}

export default function Footer() {
  return (
    <footer className="bg-slate-900 text-white">
      <div className="container-alfa py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-8">
          {/* Company Info */}
          <div className="lg:col-span-2">
            <div className="flex items-center gap-2 mb-6">
              <div className="w-8 h-8 bg-gradient-to-br from-violet-500 to-pink-500 rounded-lg flex items-center justify-center">
                <span className="text-white font-bold text-sm">A</span>
              </div>
              <span className="text-2xl font-bold">Alfa</span>
            </div>
            <p className="text-gray-300 mb-6 max-w-md">
              La plataforma de pagos más confiable de Paraguay. Compra en 4 cuotas sin interés y impulsa tu negocio.
            </p>
            <div className="flex gap-4">
              {['🌐', '📱', '💼', '📧'].map((icon, index) => (
                <div key={index} className="w-10 h-10 bg-gray-700 rounded-full flex items-center justify-center hover:bg-gray-600 cursor-pointer transition-colors">
                  <span className="text-lg">{icon}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Products */}
          <div>
            <h3 className="font-semibold mb-4">Products</h3>
            <ul className="space-y-2">
              {footerLinks.products.map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-300 hover:text-white text-sm transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Solutions */}
          <div>
            <h3 className="font-semibold mb-4">Solutions</h3>
            <ul className="space-y-2">
              {footerLinks.solutions.slice(0, 6).map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-300 hover:text-white text-sm transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Resources */}
          <div>
            <h3 className="font-semibold mb-4">Resources</h3>
            <ul className="space-y-2">
              {footerLinks.resources.slice(0, 6).map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-300 hover:text-white text-sm transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* Company */}
          <div>
            <h3 className="font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link, index) => (
                <li key={index}>
                  <Link href={link.href} className="text-gray-300 hover:text-white text-sm transition-colors">
                    {link.name}
                  </Link>
                </li>
              ))}
            </ul>
            <div className="mt-6">
              <h4 className="font-semibold mb-2">Support</h4>
              <ul className="space-y-2">
                {footerLinks.support.map((link, index) => (
                  <li key={index}>
                    <Link href={link.href} className="text-gray-300 hover:text-white text-sm transition-colors">
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-gray-700 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-gray-400 text-sm">
            © 2025 Alfa S.A. All rights reserved.
          </div>
          <div className="flex gap-6 mt-4 md:mt-0">
            <Link href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
              Privacy Policy
            </Link>
            <Link href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
              Terms of Service
            </Link>
            <Link href="#" className="text-gray-400 hover:text-white text-sm transition-colors">
              Cookie Settings
            </Link>
          </div>
        </div>
      </div>
    </footer>
  )
} 