'use client'

import { CheckIcon } from '@heroicons/react/24/solid'

const steps = [
  {
    number: '1',
    title: 'Regís<PERSON>',
    description: 'Crea tu cuenta en segundos con tu número de teléfono y email.',
    icon: '📱'
  },
  {
    number: '2',
    title: 'Elige tu producto',
    description: 'Selecciona lo que quieres comprar en cualquier negocio aliado.',
    icon: '🛍️'
  },
  {
    number: '3',
    title: 'Paga en 4 cuotas',
    description: 'Divide el pago en 4 cuotas mensuales sin intereses.',
    icon: '💳'
  },
  {
    number: '4',
    title: 'Disfruta tu compra',
    description: 'Recibe tu producto inmediatamente y paga cómodamente.',
    icon: '🎉'
  }
]

export default function HowItWorks() {
  return (
    <section className="section-alfa bg-white">
      <div className="container-alfa">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-alfa-primary mb-6">
            ¿Cómo funciona?
          </h2>
          <p className="text-xl text-alfa-secondary max-w-2xl mx-auto">
            En tan solo 5 pasos, desde hoy puedes empezar a comprar con Alfa
          </p>
        </div>

        {/* Steps */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {steps.map((step, index) => (
            <div key={index} className="relative">
              {/* Connection Line */}
              {index < steps.length - 1 && (
                <div className="hidden lg:block absolute top-16 left-full w-full h-0.5 bg-gradient-to-r from-violet-500 to-pink-500 z-0"></div>
              )}
              
              {/* Step Card */}
              <div className="relative z-10 text-center">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-violet-500 to-pink-500 rounded-full text-white font-bold text-xl mb-6">
                  {step.number}
                </div>
                
                <div className="text-4xl mb-4">{step.icon}</div>
                
                <h3 className="text-xl font-semibold text-alfa-primary mb-4">
                  {step.title}
                </h3>
                
                <p className="text-alfa-secondary leading-relaxed">
                  {step.description}
                </p>
              </div>
            </div>
          ))}
        </div>

        {/* Benefits */}
        <div className="mt-20 bg-gradient-to-r from-violet-50 to-pink-50 rounded-2xl p-8 md:p-12">
          <div className="text-center mb-8">
            <h3 className="text-2xl md:text-3xl font-bold text-alfa-primary mb-4">
              ¿Por qué elegir Alfa?
            </h3>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[
              '✅ Sin intereses ni comisiones ocultas',
              '✅ Aprobación instantánea',
              '✅ Plataforma 100% segura',
              '✅ Liquidación diaria para comercios',
              '✅ Soporte 24/7',
              '✅ Más de 100+ negocios aliados'
            ].map((benefit, index) => (
              <div key={index} className="flex items-center gap-3">
                <CheckIcon className="w-5 h-5 text-green-600 flex-shrink-0" />
                <span className="text-alfa-primary font-medium">{benefit.replace('✅ ', '')}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
} 