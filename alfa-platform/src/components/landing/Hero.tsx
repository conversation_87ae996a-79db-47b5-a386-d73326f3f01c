'use client'

import Link from 'next/link'
import { ArrowRightIcon } from '@heroicons/react/24/outline'

export default function Hero() {
  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-purple-600 via-pink-600 to-orange-500">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-600 via-pink-600 to-orange-500"></div>
      
      {/* Content */}
      <div className="relative z-10 container-alfa">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left Content */}
          <div className="text-center lg:text-left space-y-8">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold text-white leading-tight">
              4 cuotas.
              <br />
              <span className="text-orange-200">Sin intereses.</span>
              <br />
              <span className="text-pink-200">Sin letra chica.</span>
            </h1>
            
            <p className="text-xl md:text-2xl text-white/90 max-w-lg mx-auto lg:mx-0 leading-relaxed">
              Compra en tus negocios favoritos, todo en un solo lugar, y paga en 4 cuotas, sin intereses.
            </p>
            
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Link
                href="/auth/register"
                className="bg-white text-purple-600 hover:bg-gray-50 font-bold text-lg px-8 py-4 rounded-xl shadow-xl transform hover:scale-105 transition-all duration-200 inline-flex items-center justify-center gap-2"
              >
                Registrarme
                <ArrowRightIcon className="w-5 h-5" />
              </Link>
              
              <Link
                href="/auth/register?type=merchant"
                className="border-2 border-white text-white hover:bg-white hover:text-purple-600 font-bold text-lg px-8 py-4 rounded-xl transition-all duration-200 inline-flex items-center justify-center"
              >
                Soy un negocio amigo
              </Link>
            </div>
            
            <div className="inline-flex items-center gap-3 bg-white/10 backdrop-blur-sm rounded-xl px-6 py-3 border border-white/20">
              <div className="bg-blue-500 text-white px-3 py-1 rounded font-bold text-sm">
                228 × 19
              </div>
              <span className="text-white font-medium">
                Compra en tus negocios favoritos, todo en un solo lugar
              </span>
            </div>
          </div>
          
          {/* Right Content - Hero Image */}
          <div className="relative">
            <div className="relative z-10 flex justify-center lg:justify-end">
              <div className="w-full max-w-lg">
                {/* Main Hero Image Container */}
                <div className="relative">
                  <div className="w-full h-96 bg-gradient-to-br from-white/20 to-white/5 rounded-3xl border border-white/20 backdrop-blur-sm flex items-center justify-center overflow-hidden">
                    {/* Shopping Woman Illustration */}
                    <div className="text-center text-white">
                      <div className="w-32 h-32 bg-orange-400 rounded-full mx-auto mb-4 flex items-center justify-center relative">
                        <span className="text-5xl">👩‍💼</span>
                        {/* Shopping Bags */}
                        <div className="absolute -right-2 -bottom-2 w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center">
                          <span className="text-2xl">🛍️</span>
                        </div>
                      </div>
                      <p className="text-lg font-bold">¡Compra ahora!</p>
                      <p className="text-sm opacity-80">Paga después en 4 cuotas</p>
                    </div>
                  </div>
                  
                  {/* Floating Elements */}
                  <div className="absolute -top-4 -right-4 bg-blue-500 text-white font-bold text-xl px-4 py-2 rounded-xl shadow-lg animate-bounce">
                    228 × 19
                  </div>
                  
                  <div className="absolute -bottom-4 -left-4 bg-orange-500 text-white font-bold text-lg px-6 py-3 rounded-xl shadow-lg">
                    4 cuotas
                  </div>
                </div>
              </div>
            </div>
            
            {/* Decorative elements */}
            <div className="absolute top-10 left-10 w-20 h-20 bg-pink-400/20 rounded-full blur-xl animate-pulse"></div>
            <div className="absolute bottom-10 right-10 w-32 h-32 bg-orange-400/20 rounded-full blur-xl animate-pulse delay-1000"></div>
          </div>
        </div>
        
        {/* Partner Logos Preview */}
        <div className="mt-16 text-center">
          <p className="text-white/80 text-lg mb-8">Disponible en más de 100 tiendas</p>
          <div className="flex justify-center items-center gap-8 flex-wrap opacity-80">
            {['Amazon', 'Google', 'Zara', 'Shopify', 'WhatsApp', 'Marriott'].map((brand, index) => (
              <div key={index} className="bg-white/10 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/20">
                <span className="text-white font-medium text-sm">{brand}</span>
              </div>
            ))}
          </div>
        </div>
      </div>
      
      {/* Bottom Wave */}
      <div className="absolute bottom-0 left-0 right-0">
        <svg viewBox="0 0 1200 120" className="w-full h-auto">
          <path
            d="M321.39,56.44c58-10.79,114.16-30.13,172-41.86,82.39-16.72,168.19-17.73,250.45-.39C823.78,31,906.67,72,985.66,92.83c70.05,18.48,146.53,26.09,214.34,3V0H0V27.35A600.21,600.21,0,0,0,321.39,56.44Z"
            fill="white"
          />
        </svg>
      </div>
    </section>
  )
} 