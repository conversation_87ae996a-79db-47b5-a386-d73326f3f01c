'use client'

import Link from 'next/link'
import { ArrowRightIcon } from '@heroicons/react/24/outline'

export default function CTA() {
  return (
    <section className="section-alfa bg-gradient-to-r from-violet-600 via-purple-600 to-pink-600">
      <div className="container-alfa">
        <div className="text-center">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-8">
            Ready to get started?
          </h2>
          <p className="text-xl text-white/90 mb-12 max-w-2xl mx-auto">
            Explore Stripe Payments, or create an account instantly and start accepting payments. 
            You can also contact us to design a custom package for your business.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center items-center">
            <Link
              href="/auth/register"
              className="group bg-white text-violet-600 hover:bg-gray-50 font-semibold text-lg px-8 py-4 rounded-lg transition-all duration-200 inline-flex items-center gap-2"
            >
              Start now
              <ArrowRightIcon className="w-5 h-5 group-hover:translate-x-1 transition-transform" />
            </Link>
            
            <Link
              href="/auth/register?type=merchant"
              className="border-2 border-white text-white hover:bg-white hover:text-violet-600 font-semibold text-lg px-8 py-4 rounded-lg transition-all duration-200"
            >
              Contact sales
            </Link>
          </div>
          
          <div className="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 text-center">
            <div className="text-white/80">
              <div className="text-3xl mb-2">🚀</div>
              <h3 className="font-semibold mb-2">Always know what you pay</h3>
              <p className="text-sm">Integrated per-transaction pricing with no hidden fees.</p>
            </div>
            
            <div className="text-white/80">
              <div className="text-3xl mb-2">💰</div>
              <h3 className="font-semibold mb-2">Start your integration</h3>
              <p className="text-sm">Get up and running with Stripe in as little as 10 minutes.</p>
            </div>
            
            <div className="text-white/80">
              <div className="text-3xl mb-2">🛡️</div>
              <h3 className="font-semibold mb-2">API reference</h3>
              <p className="text-sm">Learn to integrate Stripe with your business.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 