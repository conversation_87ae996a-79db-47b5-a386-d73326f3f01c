'use client'

const testimonials = [
  {
    name: '<PERSON>',
    role: '<PERSON><PERSON>e Alfa',
    content: 'Alfa me cambió la forma de comprar. Ahora puedo comprar lo que necesito sin preocuparme por el pago completo. Las 4 cuotas me dan mucha flexibilidad.',
    rating: 5,
    avatar: '👩‍💼'
  },
  {
    name: '<PERSON>',
    role: 'Dueño de Tienda Fashion',
    content: 'Desde que implementamos Alfa, nuestras ventas aumentaron un 40%. Los clientes compran más porque saben que pueden pagar en cuotas sin interés.',
    rating: 5,
    avatar: '👨‍💼'
  },
  {
    name: '<PERSON>',
    role: '<PERSON><PERSON><PERSON>',
    content: 'La plataforma es súper fácil de usar. Me registré en menos de 5 minutos y ya pude hacer mi primera compra. El proceso de pago es muy intuitivo.',
    rating: 5,
    avatar: '👩‍🎓'
  },
  {
    name: '<PERSON>',
    role: 'Comercia<PERSON>',
    content: 'La liquidación diaria es perfecta para nuestro flujo de caja. Recibimos el pago completo al día siguiente y nuestros clientes están felices.',
    rating: 5,
    avatar: '👨‍🔧'
  }
]

export default function Testimonials() {
  return (
    <section className="section-alfa bg-white">
      <div className="container-alfa">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-alfa-primary mb-6">
            Lo que dicen nuestros usuarios
          </h2>
          <p className="text-xl text-alfa-secondary max-w-2xl mx-auto">
            Miles de personas ya confían en Alfa para sus compras diarias
          </p>
        </div>

        {/* Testimonials Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <div key={index} className="card-alfa p-8">
              <div className="flex items-center mb-6">
                <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-pink-500 rounded-full flex items-center justify-center text-white text-xl mr-4">
                  {testimonial.avatar}
                </div>
                <div>
                  <h4 className="font-semibold text-alfa-primary">
                    {testimonial.name}
                  </h4>
                  <p className="text-sm text-alfa-muted">
                    {testimonial.role}
                  </p>
                </div>
              </div>
              
              <p className="text-alfa-secondary mb-4 leading-relaxed">
                "{testimonial.content}"
              </p>
              
              <div className="flex items-center">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <span key={i} className="text-yellow-400 text-lg">⭐</span>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Social Proof */}
        <div className="mt-16 text-center">
          <div className="inline-flex items-center gap-6 bg-gray-50 rounded-full px-8 py-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-alfa-primary">4.8/5</div>
              <div className="text-sm text-alfa-muted">Calificación promedio</div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-alfa-primary">10k+</div>
              <div className="text-sm text-alfa-muted">Usuarios activos</div>
            </div>
            <div className="w-px h-12 bg-gray-200"></div>
            <div className="text-center">
              <div className="text-2xl font-bold text-alfa-primary">500+</div>
              <div className="text-sm text-alfa-muted">Comercios aliados</div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 