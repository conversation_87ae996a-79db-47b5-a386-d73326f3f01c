'use client'

const partners = [
  {
    name: 'Amazon',
    logo: '/logos/amazon.png',
    category: 'E-commerce',
    color: 'bg-orange-100 text-orange-600'
  },
  {
    name: 'Salesforce',
    logo: '/logos/salesforce.png', 
    category: 'CRM',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: 'Google',
    logo: '/logos/google.png',
    category: 'Tech',
    color: 'bg-red-100 text-red-600'
  },
  {
    name: '<PERSON><PERSON>',
    logo: '/logos/zara.png',
    category: 'Fashion',
    color: 'bg-gray-100 text-gray-600'
  },
  {
    name: 'Shopify',
    logo: '/logos/shopify.png',
    category: 'E-commerce',
    color: 'bg-green-100 text-green-600'
  },
  {
    name: 'WhatsApp',
    logo: '/logos/whatsapp.png',
    category: 'Communication',
    color: 'bg-green-100 text-green-600'
  },
  {
    name: 'Transferwise',
    logo: '/logos/transferwise.png',
    category: 'Fintech',
    color: 'bg-blue-100 text-blue-600'
  },
  {
    name: '<PERSON><PERSON><PERSON>',
    logo: '/logos/marriott.png',
    category: 'Hospitality',
    color: 'bg-purple-100 text-purple-600'
  }
]

export default function Partners() {
  return (
    <section className="py-20 bg-white">
      <div className="container-alfa">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Usa Alfa en +100 de tus
            <br />
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              tiendas favoritas
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            Nuestra plataforma está integrada con los mejores comercios del país
          </p>
        </div>

        {/* Partners Grid - First Row */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-8">
          {partners.slice(0, 4).map((partner, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 text-center border border-gray-100 hover:scale-105">
              <div className={`w-16 h-16 ${partner.color} rounded-xl mx-auto mb-4 flex items-center justify-center font-bold text-lg`}>
                {partner.name.charAt(0)}
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-1">
                {partner.name}
              </h4>
              <p className="text-sm text-gray-500">
                {partner.category}
              </p>
            </div>
          ))}
        </div>

        {/* Partners Grid - Second Row */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-16">
          {partners.slice(4, 8).map((partner, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 text-center border border-gray-100 hover:scale-105">
              <div className={`w-16 h-16 ${partner.color} rounded-xl mx-auto mb-4 flex items-center justify-center font-bold text-lg`}>
                {partner.name.charAt(0)}
              </div>
              <h4 className="text-lg font-bold text-gray-900 mb-1">
                {partner.name}
              </h4>
              <p className="text-sm text-gray-500">
                {partner.category}
              </p>
            </div>
          ))}
        </div>

        {/* Ready to get started section */}
        <div className="text-center">
          <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
            Ready to get started?
          </h3>
          <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto">
            Explore Alfa Payments, or create an account instantly and start accepting payments. 
            You can also contact us to design a custom package for your business.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center items-center mb-12">
            <button className="bg-gradient-to-r from-purple-600 to-pink-600 text-white font-bold text-lg px-8 py-4 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 hover:scale-105">
              Start now
            </button>
            <button className="border-2 border-purple-600 text-purple-600 hover:bg-purple-50 font-bold text-lg px-8 py-4 rounded-xl transition-all duration-200">
              Contact sales
            </button>
          </div>

          {/* Features */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">💰</span>
              </div>
              <h4 className="font-bold text-gray-900 mb-2">Always know what you pay</h4>
              <p className="text-sm text-gray-600">Integrated per-transaction pricing with no hidden fees.</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-pink-500 to-orange-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">🚀</span>
              </div>
              <h4 className="font-bold text-gray-900 mb-2">Start your integration</h4>
              <p className="text-sm text-gray-600">Get up and running with Alfa in as little as 10 minutes.</p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-br from-orange-500 to-yellow-500 rounded-2xl mx-auto mb-4 flex items-center justify-center">
                <span className="text-2xl">📚</span>
              </div>
              <h4 className="font-bold text-gray-900 mb-2">API reference</h4>
              <p className="text-sm text-gray-600">Learn to integrate Alfa with your business.</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
} 