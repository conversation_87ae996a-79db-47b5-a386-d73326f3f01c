'use client'

import { 
  CreditCardIcon, 
  ShieldCheckIcon, 
  ClockIcon, 
  ChartBarIcon,
  BanknotesIcon,
  UserGroupIcon 
} from '@heroicons/react/24/outline'

const customerFeatures = [
  {
    icon: CreditCardIcon,
    title: '4 cuotas sin interés',
    description: 'Divide tus compras en 4 pagos iguales sin intereses ni comisiones adicionales.'
  },
  {
    icon: ShieldCheckIcon,
    title: 'Seguro y confiable',
    description: 'Plataforma segura con la mejor tecnología de encriptación para proteger tus datos.'
  },
  {
    icon: ClockIcon,
    title: 'Aprobación instantánea',
    description: 'Proceso de verificación rápido y aprobación en segundos.'
  }
]

const merchantFeatures = [
  {
    icon: ChartBarIcon,
    title: 'Aumenta tus ventas',
    description: 'Los clientes compran más cuando pueden pagar en cuotas sin interés.'
  },
  {
    icon: BanknotesIcon,
    title: 'Liquidación diaria',
    description: 'Recibe el pago completo en tu cuenta al día siguiente de la compra.'
  },
  {
    icon: UserGroupIcon,
    title: 'Nuevos clientes',
    description: 'Atrae clientes que no tienen tarjeta de crédito o prefieren no usarla.'
  }
]

export default function Features() {
  return (
    <section className="py-20 bg-gray-50">
      <div className="container-alfa">
        {/* Header */}
        <div className="text-center mb-16">
          <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-gray-900 mb-6 leading-tight">
            Con alfa, date ese 
            <br />
            <span className="bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent">
              mimo que te mereces.
            </span>
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
            Reduce costs, grow revenue, and run your business more efficiently on a fully integrated platform. 
            Use Stripe to handle all of your payments-related needs, manage revenue operations, and launch (or invent) new business models.
          </p>
        </div>

        {/* Customer Features */}
        <div className="mb-20">
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Para ti como cliente
            </h3>
            <p className="text-lg text-gray-600">
              Disfruta de una experiencia de compra sin complicaciones
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {customerFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 p-8 text-center group hover:scale-105">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-purple-500 to-pink-500 rounded-2xl mb-6 group-hover:scale-110 transition-transform">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-4">
                  {feature.title}
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Merchant Features */}
        <div>
          <div className="text-center mb-12">
            <h3 className="text-2xl md:text-3xl font-bold text-gray-900 mb-4">
              Para tu negocio
            </h3>
            <p className="text-lg text-gray-600">
              Impulsa tus ventas y atrae nuevos clientes
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {merchantFeatures.map((feature, index) => (
              <div key={index} className="bg-white rounded-2xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300 p-8 text-center group hover:scale-105">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-orange-500 to-red-500 rounded-2xl mb-6 group-hover:scale-110 transition-transform">
                  <feature.icon className="w-8 h-8 text-white" />
                </div>
                <h4 className="text-xl font-bold text-gray-900 mb-4">
                  {feature.title}
                </h4>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </section>
  )
} 