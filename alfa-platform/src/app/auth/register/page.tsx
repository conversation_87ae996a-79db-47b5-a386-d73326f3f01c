'use client'

import { useState, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import Link from 'next/link'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { ArrowLeftIcon, EyeIcon, EyeSlashIcon } from '@heroicons/react/24/outline'
import { userRegistrationSchema, type UserRegistration } from '@/lib/validations'
import { supabase } from '@/lib/supabase'
import { useAuth } from '@/lib/store'
import { useRouter } from 'next/navigation'

export default function RegisterPage() {
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [errorMessage, setErrorMessage] = useState('')
  const [step, setStep] = useState(1) // 1: Form, 2: SMS Verification
  const [phoneNumber, setPhoneNumber] = useState('')
  const [smsCode, setSmsCode] = useState('')
  const [verificationId, setVerificationId] = useState('')
  
  const { setUser } = useAuth()
  const router = useRouter()
  const searchParams = useSearchParams()
  const userType = searchParams.get('type') || 'customer'

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<UserRegistration>({
    resolver: zodResolver(userRegistrationSchema),
  })

  const onSubmit = async (data: UserRegistration) => {
    setIsLoading(true)
    setErrorMessage('')

    try {
      // Create auth user first
      const { data: authData, error: authError } = await supabase.auth.signUp({
        email: data.email,
        password: 'temp_password_' + Math.random().toString(36).substring(7),
        options: {
          data: {
            full_name: data.full_name,
            phone: data.phone,
          },
        },
      })

      if (authError) {
        setErrorMessage(authError.message)
        return
      }

      if (authData.user) {
        // Store user data for after SMS verification
        localStorage.setItem('pending_user_data', JSON.stringify({
          ...data,
          user_id: authData.user.id,
          user_type: userType,
        }))

        setPhoneNumber(data.phone)
        setStep(2)
        
        // Here you would trigger SMS verification
        // For now, we'll simulate it
        console.log('SMS sent to:', data.phone)
        setVerificationId('mock_verification_id')
      }
    } catch (error) {
      setErrorMessage('Error al crear la cuenta. Inténtalo de nuevo.')
    } finally {
      setIsLoading(false)
    }
  }

  const handleSmsVerification = async () => {
    if (!smsCode || smsCode.length !== 6) {
      setErrorMessage('Por favor ingresa un código de 6 dígitos')
      return
    }

    setIsLoading(true)
    setErrorMessage('')

    try {
      // In production, verify SMS code with your SMS provider
      if (smsCode === '123456') { // Mock verification
        const pendingData = localStorage.getItem('pending_user_data')
        if (pendingData) {
          const userData = JSON.parse(pendingData)
          
          // Create user profile
          const { error: profileError } = await supabase
            .from('users')
            .insert([
              {
                id: userData.user_id,
                email: userData.email,
                full_name: userData.full_name,
                phone: userData.phone,
                age: userData.age,
                address: userData.address,
              },
            ])

          if (profileError) {
            setErrorMessage('Error al crear el perfil')
            return
          }

          // If merchant type, redirect to merchant onboarding
          if (userType === 'merchant') {
            router.push('/auth/merchant-onboarding')
          } else {
            router.push('/dashboard')
          }
          
          localStorage.removeItem('pending_user_data')
        }
      } else {
        setErrorMessage('Código de verificación inválido')
      }
    } catch (error) {
      setErrorMessage('Error al verificar el código')
    } finally {
      setIsLoading(false)
    }
  }

  if (step === 2) {
    return (
      <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
        <div className="sm:mx-auto sm:w-full sm:max-w-md">
          <div className="flex justify-center">
            <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-pink-500 rounded-xl flex items-center justify-center">
              <span className="text-white font-bold text-xl">A</span>
            </div>
          </div>
          
          <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
            Verifica tu teléfono
          </h2>
          <p className="mt-2 text-center text-sm text-gray-600">
            Enviamos un código de 6 dígitos a<br />
            <span className="font-medium">{phoneNumber}</span>
          </p>
        </div>

        <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
          <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
            {errorMessage && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
                <p className="text-sm text-red-800">{errorMessage}</p>
              </div>
            )}

            <div className="space-y-6">
              <div>
                <label htmlFor="sms-code" className="block text-sm font-medium text-gray-700">
                  Código de verificación
                </label>
                <div className="mt-1">
                  <input
                    type="text"
                    value={smsCode}
                    onChange={(e) => setSmsCode(e.target.value)}
                    className="input-alfa text-center text-2xl font-bold tracking-widest"
                    placeholder="000000"
                    maxLength={6}
                  />
                </div>
              </div>

              <button
                onClick={handleSmsVerification}
                disabled={isLoading || smsCode.length !== 6}
                className="button-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Verificando...' : 'Verificar código'}
              </button>

              <div className="text-center">
                <p className="text-sm text-gray-500">
                  ¿No recibiste el código?{' '}
                  <button className="font-medium text-violet-600 hover:text-violet-500">
                    Reenviar SMS
                  </button>
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
      <div className="sm:mx-auto sm:w-full sm:max-w-md">
        <Link
          href="/"
          className="inline-flex items-center gap-2 text-gray-600 hover:text-gray-900 mb-6"
        >
          <ArrowLeftIcon className="w-5 h-5" />
          Volver al inicio
        </Link>

        <div className="flex justify-center">
          <div className="w-12 h-12 bg-gradient-to-br from-violet-500 to-pink-500 rounded-xl flex items-center justify-center">
            <span className="text-white font-bold text-xl">A</span>
          </div>
        </div>
        
        <h2 className="mt-6 text-center text-3xl font-bold text-gray-900">
          {userType === 'merchant' ? 'Registro de Comercio' : 'Crea tu cuenta'}
        </h2>
        <p className="mt-2 text-center text-sm text-gray-600">
          ¿Ya tienes una cuenta?{' '}
          <Link href="/auth/login" className="font-medium text-violet-600 hover:text-violet-500">
            Inicia sesión
          </Link>
        </p>
      </div>

      <div className="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
        <div className="bg-white py-8 px-4 shadow sm:rounded-lg sm:px-10">
          <form className="space-y-6" onSubmit={handleSubmit(onSubmit)}>
            {errorMessage && (
              <div className="bg-red-50 border border-red-200 rounded-md p-4">
                <p className="text-sm text-red-800">{errorMessage}</p>
              </div>
            )}

            <div>
              <label htmlFor="full_name" className="block text-sm font-medium text-gray-700">
                Nombre completo
              </label>
              <div className="mt-1">
                <input
                  {...register('full_name')}
                  type="text"
                  className="input-alfa"
                  placeholder="Juan Pérez"
                />
              </div>
              {errors.full_name && (
                <p className="mt-2 text-sm text-red-600">{errors.full_name.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700">
                Email
              </label>
              <div className="mt-1">
                <input
                  {...register('email')}
                  type="email"
                  autoComplete="email"
                  className="input-alfa"
                  placeholder="<EMAIL>"
                />
              </div>
              {errors.email && (
                <p className="mt-2 text-sm text-red-600">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700">
                Teléfono
              </label>
              <div className="mt-1">
                <input
                  {...register('phone')}
                  type="tel"
                  className="input-alfa"
                  placeholder="987654321"
                />
              </div>
              {errors.phone && (
                <p className="mt-2 text-sm text-red-600">{errors.phone.message}</p>
              )}
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="age" className="block text-sm font-medium text-gray-700">
                  Edad
                </label>
                <div className="mt-1">
                  <input
                    {...register('age', { valueAsNumber: true })}
                    type="number"
                    className="input-alfa"
                    placeholder="25"
                  />
                </div>
                {errors.age && (
                  <p className="mt-2 text-sm text-red-600">{errors.age.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="address" className="block text-sm font-medium text-gray-700">
                Dirección
              </label>
              <div className="mt-1">
                <textarea
                  {...register('address')}
                  className="input-alfa"
                  rows={3}
                  placeholder="Calle 123, Barrio Centro, Asunción"
                />
              </div>
              {errors.address && (
                <p className="mt-2 text-sm text-red-600">{errors.address.message}</p>
              )}
            </div>

            <div className="flex items-center">
              <input
                {...register('terms')}
                id="terms"
                type="checkbox"
                className="h-4 w-4 text-violet-600 focus:ring-violet-500 border-gray-300 rounded"
              />
              <label htmlFor="terms" className="ml-2 block text-sm text-gray-900">
                Acepto los{' '}
                <Link href="/terms" className="text-violet-600 hover:text-violet-500">
                  términos y condiciones
                </Link>{' '}
                y la{' '}
                <Link href="/privacy" className="text-violet-600 hover:text-violet-500">
                  política de privacidad
                </Link>
              </label>
            </div>
            {errors.terms && (
              <p className="mt-2 text-sm text-red-600">{errors.terms.message}</p>
            )}

            <div>
              <button
                type="submit"
                disabled={isLoading}
                className="button-primary w-full disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isLoading ? 'Creando cuenta...' : 'Crear cuenta'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
} 