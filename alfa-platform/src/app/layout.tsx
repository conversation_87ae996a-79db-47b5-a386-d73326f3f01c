import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "Alfa - Compra en 4 cuotas sin interés",
  description: "La plataforma de pagos más confiable de Paraguay. Compra en 4 cuotas sin interés y sin comisiones. Disponible en más de 100 tiendas.",
  keywords: "pagos, cuotas, Paraguay, BNPL, buy now pay later, fintech",
  authors: [{ name: "Alfa S.A." }],
  creator: "Alfa",
  publisher: "Alfa S.A.",
  openGraph: {
    title: "Alfa - Compra en 4 cuotas sin interés",
    description: "La plataforma de pagos más confiable de Paraguay. Compra en 4 cuotas sin interés y sin comisiones.",
    url: "https://alfa.com.py",
    siteName: "Alfa",
    locale: "es_PY",
    type: "website",
  },
  twitter: {
    card: "summary_large_image",
    title: "Alfa - Compra en 4 cuotas sin interés",
    description: "La plataforma de pagos más confiable de Paraguay.",
    creator: "@AlfaPY",
  },
  robots: {
    index: true,
    follow: true,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="es">
      <body className={`${inter.variable} font-sans antialiased`}>
        {children}
      </body>
    </html>
  );
}
