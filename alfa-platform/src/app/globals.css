@tailwind base;
@tailwind components;
@tailwind utilities;

@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@layer base {
  body {
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .button-primary {
    @apply bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white font-bold py-3 px-6 rounded-xl shadow-lg transform hover:scale-105 transition-all duration-200;
  }
  
  .button-secondary {
    @apply bg-white text-purple-600 font-bold py-3 px-6 rounded-xl border-2 border-purple-600 hover:bg-purple-50 transition-all duration-200;
  }
  
  .card-alfa {
    @apply bg-white rounded-2xl shadow-xl border border-gray-100 hover:shadow-2xl transition-all duration-300;
  }
  
  .input-alfa {
    @apply w-full px-4 py-3 border border-gray-200 rounded-xl focus:ring-2 focus:ring-purple-500 focus:border-purple-500 transition-all duration-200 bg-white;
  }
  
  .container-alfa {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
  
  .section-alfa {
    @apply py-16 sm:py-20 lg:py-24;
  }
}

@layer utilities {
  .text-gradient {
    @apply bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent;
  }
  
  .shadow-alfa {
    box-shadow: 0 25px 50px -12px rgba(139, 92, 246, 0.25);
  }
  
  .border-gradient {
    border: 1px solid transparent;
    background: linear-gradient(white, white) padding-box,
                linear-gradient(135deg, #8B5CF6, #EC4899) border-box;
  }
}
