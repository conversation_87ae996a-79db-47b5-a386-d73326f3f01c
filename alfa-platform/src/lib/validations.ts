import { z } from 'zod'

// User Registration Schema
export const userRegistrationSchema = z.object({
  email: z.string().email('Email inválido'),
  full_name: z.string().min(2, 'El nombre debe tener al menos 2 caracteres'),
  phone: z.string().min(9, 'El teléfono debe tener 9 dígitos').regex(/^9\d{8}$/, 'Formato de teléfono inválido'),
  age: z.number().min(18, 'Debes ser mayor de 18 años').max(100, 'Edad inválida'),
  address: z.string().min(10, 'La dirección debe tener al menos 10 caracteres'),
  terms: z.boolean().refine((val) => val === true, 'Debes aceptar los términos y condiciones'),
})

// Merchant Registration Schema
export const merchantRegistrationSchema = z.object({
  business_name: z.string().min(2, 'El nombre del negocio debe tener al menos 2 caracteres'),
  ruc: z.string().min(6, 'El RUC debe tener al menos 6 dígitos').max(8, 'El RUC no puede tener más de 8 dígitos'),
  business_type: z.string().min(2, 'El tipo de negocio es requerido'),
  average_ticket: z.number().min(1000, 'El ticket promedio debe ser mayor a 1,000 PYG'),
  monthly_revenue: z.number().min(1000000, 'La facturación mensual debe ser mayor a 1,000,000 PYG'),
  contact_email: z.string().email('Email inválido'),
  contact_phone: z.string().min(9, 'El teléfono debe tener 9 dígitos').regex(/^9\d{8}$/, 'Formato de teléfono inválido'),
  terms: z.boolean().refine((val) => val === true, 'Debes aceptar los términos y condiciones'),
})

// Login Schema
export const loginSchema = z.object({
  email: z.string().email('Email inválido'),
  password: z.string().min(6, 'La contraseña debe tener al menos 6 caracteres'),
})

// SMS Verification Schema
export const smsVerificationSchema = z.object({
  phone: z.string().min(9, 'El teléfono debe tener 9 dígitos').regex(/^9\d{8}$/, 'Formato de teléfono inválido'),
  code: z.string().length(6, 'El código debe tener 6 dígitos').optional(),
})

// Payment Schema
export const paymentSchema = z.object({
  purchase_id: z.string().uuid('ID de compra inválido'),
  installment_number: z.number().min(1).max(4),
  payment_method: z.enum(['bancard_qr', 'bank_transfer', 'debit_card']),
  amount: z.number().min(1, 'El monto debe ser mayor a 0'),
  receipt_image: z.string().optional(),
})

// Purchase Schema
export const purchaseSchema = z.object({
  merchant_id: z.string().uuid('ID de comercio inválido'),
  amount: z.number().min(1000, 'El monto mínimo es 1,000 PYG'),
  installments: z.number().min(1).max(4).default(4),
  description: z.string().optional(),
})

// Profile Update Schema
export const profileUpdateSchema = z.object({
  full_name: z.string().min(2, 'El nombre debe tener al menos 2 caracteres').optional(),
  phone: z.string().min(9, 'El teléfono debe tener 9 dígitos').regex(/^9\d{8}$/, 'Formato de teléfono inválido').optional(),
  address: z.string().min(10, 'La dirección debe tener al menos 10 caracteres').optional(),
  age: z.number().min(18, 'Debes ser mayor de 18 años').max(100, 'Edad inválida').optional(),
})

// Contact Form Schema
export const contactFormSchema = z.object({
  name: z.string().min(2, 'El nombre debe tener al menos 2 caracteres'),
  email: z.string().email('Email inválido'),
  phone: z.string().min(9, 'El teléfono debe tener 9 dígitos').regex(/^9\d{8}$/, 'Formato de teléfono inválido'),
  message: z.string().min(10, 'El mensaje debe tener al menos 10 caracteres'),
  subject: z.string().min(3, 'El asunto debe tener al menos 3 caracteres'),
})

// Password Reset Schema
export const passwordResetSchema = z.object({
  email: z.string().email('Email inválido'),
})

// Change Password Schema
export const changePasswordSchema = z.object({
  current_password: z.string().min(6, 'La contraseña actual es requerida'),
  new_password: z.string().min(6, 'La nueva contraseña debe tener al menos 6 caracteres'),
  confirm_password: z.string().min(6, 'La confirmación de contraseña es requerida'),
}).refine((data) => data.new_password === data.confirm_password, {
  message: 'Las contraseñas no coinciden',
  path: ['confirm_password'],
})

// Bank Account Schema
export const bankAccountSchema = z.object({
  bank_name: z.string().min(2, 'El nombre del banco es requerido'),
  account_number: z.string().min(8, 'El número de cuenta debe tener al menos 8 dígitos'),
  account_type: z.enum(['checking', 'savings'], { message: 'Tipo de cuenta inválido' }),
  account_holder: z.string().min(2, 'El nombre del titular es requerido'),
})

// AI Chat Schema
export const aiChatSchema = z.object({
  message: z.string().min(1, 'El mensaje no puede estar vacío').max(1000, 'El mensaje no puede tener más de 1000 caracteres'),
  context: z.enum(['customer', 'merchant', 'general']).optional(),
})

// Export type helpers
export type UserRegistration = z.infer<typeof userRegistrationSchema>
export type MerchantRegistration = z.infer<typeof merchantRegistrationSchema>
export type Login = z.infer<typeof loginSchema>
export type SmsVerification = z.infer<typeof smsVerificationSchema>
export type Payment = z.infer<typeof paymentSchema>
export type Purchase = z.infer<typeof purchaseSchema>
export type ProfileUpdate = z.infer<typeof profileUpdateSchema>
export type ContactForm = z.infer<typeof contactFormSchema>
export type PasswordReset = z.infer<typeof passwordResetSchema>
export type ChangePassword = z.infer<typeof changePasswordSchema>
export type BankAccount = z.infer<typeof bankAccountSchema>
export type AiChat = z.infer<typeof aiChatSchema> 