import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

// Types for our database
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          phone: string
          age: number
          address: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          phone: string
          age: number
          address: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          phone?: string
          age?: number
          address?: string
          created_at?: string
          updated_at?: string
        }
      }
      merchants: {
        Row: {
          id: string
          user_id: string
          business_name: string
          ruc: string
          business_type: string
          average_ticket: number
          monthly_revenue: number
          status: 'pending' | 'approved' | 'rejected'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          business_name: string
          ruc: string
          business_type: string
          average_ticket: number
          monthly_revenue: number
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          business_name?: string
          ruc?: string
          business_type?: string
          average_ticket?: number
          monthly_revenue?: number
          status?: 'pending' | 'approved' | 'rejected'
          created_at?: string
          updated_at?: string
        }
      }
      purchases: {
        Row: {
          id: string
          user_id: string
          merchant_id: string
          amount: number
          installments: number
          status: 'active' | 'completed' | 'defaulted'
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          merchant_id: string
          amount: number
          installments: number
          status?: 'active' | 'completed' | 'defaulted'
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          merchant_id?: string
          amount?: number
          installments?: number
          status?: 'active' | 'completed' | 'defaulted'
          created_at?: string
          updated_at?: string
        }
      }
      payments: {
        Row: {
          id: string
          purchase_id: string
          installment_number: number
          amount: number
          due_date: string
          status: 'pending' | 'paid' | 'overdue' | 'verifying'
          payment_method: string
          payment_date: string | null
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          purchase_id: string
          installment_number: number
          amount: number
          due_date: string
          status?: 'pending' | 'paid' | 'overdue' | 'verifying'
          payment_method?: string
          payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          purchase_id?: string
          installment_number?: number
          amount?: number
          due_date?: string
          status?: 'pending' | 'paid' | 'overdue' | 'verifying'
          payment_method?: string
          payment_date?: string | null
          created_at?: string
          updated_at?: string
        }
      }
    }
  }
} 