import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { supabase } from './supabase'
import type { User } from '@supabase/supabase-js'

interface UserProfile {
  id: string
  email: string
  full_name: string
  phone: string
  age: number
  address: string
  created_at: string
  updated_at: string
}

interface MerchantProfile {
  id: string
  user_id: string
  business_name: string
  ruc: string
  business_type: string
  average_ticket: number
  monthly_revenue: number
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  updated_at: string
}

interface Purchase {
  id: string
  user_id: string
  merchant_id: string
  amount: number
  installments: number
  status: 'active' | 'completed' | 'defaulted'
  created_at: string
  updated_at: string
}

interface Payment {
  id: string
  purchase_id: string
  installment_number: number
  amount: number
  due_date: string
  status: 'pending' | 'paid' | 'overdue' | 'verifying'
  payment_method: string
  payment_date: string | null
  created_at: string
  updated_at: string
}

interface AuthState {
  user: User | null
  userProfile: UserProfile | null
  merchantProfile: MerchantProfile | null
  isLoading: boolean
  isAuthenticated: boolean
  userType: 'customer' | 'merchant' | 'admin' | null
  setUser: (user: User | null) => void
  setUserProfile: (profile: UserProfile | null) => void
  setMerchantProfile: (profile: MerchantProfile | null) => void
  setUserType: (type: 'customer' | 'merchant' | 'admin' | null) => void
  signOut: () => Promise<void>
  loadUserData: () => Promise<void>
}

interface AppState {
  purchases: Purchase[]
  payments: Payment[]
  notifications: Array<{
    id: string
    type: 'info' | 'success' | 'warning' | 'error'
    message: string
    timestamp: Date
  }>
  isLoading: boolean
  setPurchases: (purchases: Purchase[]) => void
  setPayments: (payments: Payment[]) => void
  addNotification: (notification: Omit<AppState['notifications'][0], 'id' | 'timestamp'>) => void
  removeNotification: (id: string) => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      userProfile: null,
      merchantProfile: null,
      isLoading: false,
      isAuthenticated: false,
      userType: null,
      
      setUser: (user) => set({ 
        user, 
        isAuthenticated: !!user,
        userType: user ? get().userType : null
      }),
      
      setUserProfile: (profile) => set({ userProfile: profile }),
      
      setMerchantProfile: (profile) => set({ merchantProfile: profile }),
      
      setUserType: (type) => set({ userType: type }),
      
      signOut: async () => {
        await supabase.auth.signOut()
        set({
          user: null,
          userProfile: null,
          merchantProfile: null,
          isAuthenticated: false,
          userType: null,
        })
      },
      
      loadUserData: async () => {
        const { user } = get()
        if (!user) return
        
        set({ isLoading: true })
        
        try {
          // Load user profile
          const { data: profile } = await supabase
            .from('users')
            .select('*')
            .eq('id', user.id)
            .single()
          
          if (profile) {
            set({ userProfile: profile })
          }
          
          // Check if user is a merchant
          const { data: merchant } = await supabase
            .from('merchants')
            .select('*')
            .eq('user_id', user.id)
            .single()
          
          if (merchant) {
            set({ merchantProfile: merchant, userType: 'merchant' })
          } else {
            set({ userType: 'customer' })
          }
          
          // Check if user is admin
          if (user.email === process.env.ADMIN_EMAIL) {
            set({ userType: 'admin' })
          }
        } catch (error) {
          console.error('Error loading user data:', error)
        } finally {
          set({ isLoading: false })
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        userProfile: state.userProfile,
        merchantProfile: state.merchantProfile,
        isAuthenticated: state.isAuthenticated,
        userType: state.userType,
      }),
    }
  )
)

export const useAppStore = create<AppState>()((set) => ({
  purchases: [],
  payments: [],
  notifications: [],
  isLoading: false,
  
  setPurchases: (purchases) => set({ purchases }),
  
  setPayments: (payments) => set({ payments }),
  
  addNotification: (notification) => 
    set((state) => ({
      notifications: [
        ...state.notifications,
        {
          ...notification,
          id: Date.now().toString(),
          timestamp: new Date(),
        },
      ],
    })),
  
  removeNotification: (id) =>
    set((state) => ({
      notifications: state.notifications.filter((n) => n.id !== id),
    })),
  
  setLoading: (loading) => set({ isLoading: loading }),
}))

// Helper hooks
export const useAuth = () => {
  const auth = useAuthStore()
  return auth
}

export const useApp = () => {
  const app = useAppStore()
  return app
} 