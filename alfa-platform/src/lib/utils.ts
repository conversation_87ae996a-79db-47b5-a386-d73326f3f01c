import { type ClassValue, clsx } from 'clsx'
import { twMerge } from 'tailwind-merge'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatCurrency(amount: number, currency: string = 'PYG'): string {
  return new Intl.NumberFormat('es-PY', {
    style: 'currency',
    currency: currency,
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

export function formatDate(date: string | Date): string {
  return new Intl.DateTimeFormat('es-PY', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  }).format(new Date(date))
}

export function formatPhone(phone: string): string {
  // Format Paraguay phone numbers
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 9) {
    return `+595 ${cleaned.slice(0, 3)} ${cleaned.slice(3, 6)} ${cleaned.slice(6)}`
  }
  return phone
}

export function generateQRCode(data: string): string {
  // This would integrate with Bancard QR API
  // For now, return a mock QR code URL
  return `https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=${encodeURIComponent(data)}`
}

export function calculateInstallmentAmount(totalAmount: number, installments: number = 4): number {
  return Math.ceil(totalAmount / installments)
}

export function getInstallmentDates(startDate: Date, installments: number = 4): Date[] {
  const dates = []
  for (let i = 0; i < installments; i++) {
    const date = new Date(startDate)
    date.setMonth(date.getMonth() + i)
    dates.push(date)
  }
  return dates
}

export function getPaymentStatus(dueDate: string, paidDate?: string | null): 'pending' | 'paid' | 'overdue' {
  if (paidDate) return 'paid'
  if (new Date(dueDate) < new Date()) return 'overdue'
  return 'pending'
}

export function truncateText(text: string, maxLength: number = 50): string {
  if (text.length <= maxLength) return text
  return text.substring(0, maxLength) + '...'
}

export function validateRUC(ruc: string): boolean {
  // Basic RUC validation for Paraguay
  const cleaned = ruc.replace(/\D/g, '')
  return cleaned.length >= 6 && cleaned.length <= 8
}

export function validatePhone(phone: string): boolean {
  // Paraguay phone number validation
  const cleaned = phone.replace(/\D/g, '')
  return cleaned.length === 9 && cleaned.startsWith('9')
}

export function debounce<T extends (...args: any[]) => void>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func(...args), delay)
  }
} 