# Alfa - MVP Platform

## Overview

Alfa is a Paraguayan "Buy Now, Pay Later" (BNPL) digital platform that allows users to pay in 4 installments without interest and helps merchants increase sales without requiring credit cards. This MVP is designed to validate the business model with a controlled population and selected merchants.

## 🎯 Key Features

### For Customers
- **4 Interest-Free Installments**: Pay in 4 equal monthly installments
- **No Hidden Fees**: Transparent pricing with no surprises
- **Instant Approval**: Quick verification process
- **Secure Platform**: Best-in-class security and encryption
- **AI Assistant**: Get help with payments and account management
- **Multiple Payment Methods**: QR codes, bank transfers, and card payments

### For Merchants
- **Increase Sales**: Customers spend more when they can pay in installments
- **Daily Settlements**: Receive full payment the next business day
- **New Customer Acquisition**: Attract customers who prefer installment payments
- **Easy Integration**: Simple setup and management
- **Real-time Analytics**: Track sales and customer behavior
- **AI Support**: Get insights and manage your business

### For Administrators
- **Merchant Approval**: Manual approval process for new merchants
- **Analytics Dashboard**: Monitor platform performance
- **User Management**: Oversee customer and merchant accounts
- **Payment Processing**: Handle disputes and verification
- **Reporting**: Generate comprehensive reports

## 🏗️ Architecture

### Tech Stack
- **Frontend**: Next.js 15 with TypeScript
- **Styling**: Tailwind CSS with custom design system
- **Backend**: Supabase (PostgreSQL, Auth, Storage, Edge Functions)
- **State Management**: Zustand with persistence
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts for analytics
- **Icons**: Heroicons
- **Authentication**: Supabase Auth with SMS verification
- **AI**: Azure OpenAI integration
- **Payments**: Bancard QR, bank transfers, card processing

### Project Structure
```
alfa-platform/
├── src/
│   ├── app/                 # Next.js App Router
│   │   ├── auth/           # Authentication pages
│   │   ├── dashboard/      # Customer dashboard
│   │   ├── merchant/       # Merchant portal
│   │   ├── admin/          # Admin panel
│   │   └── api/            # API routes
│   ├── components/         # Reusable components
│   │   ├── landing/        # Landing page sections
│   │   ├── auth/           # Authentication components
│   │   ├── dashboard/      # Dashboard components
│   │   └── ui/             # UI components
│   ├── lib/                # Utilities and configurations
│   │   ├── supabase.ts     # Supabase client
│   │   ├── store.ts        # Zustand stores
│   │   ├── validations.ts  # Zod schemas
│   │   └── utils.ts        # Helper functions
│   └── styles/             # Global styles
├── database/               # Database schemas
├── public/                 # Static assets
└── docs/                   # Documentation
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn
- Supabase account
- Azure OpenAI account (for AI features)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd alfa-platform
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

   # Azure OpenAI Configuration
   AZURE_OPENAI_API_KEY=your_azure_openai_key
   AZURE_OPENAI_ENDPOINT=your_azure_openai_endpoint
   AZURE_OPENAI_DEPLOYMENT=your_deployment_name

   # PostHog Analytics
   NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key
   NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

   # Payment Configuration
   BANCARD_PUBLIC_KEY=your_bancard_public_key
   BANCARD_PRIVATE_KEY=your_bancard_private_key

   # App Configuration
   NEXT_PUBLIC_APP_URL=http://localhost:3000
   ADMIN_EMAIL=<EMAIL>
   ```

4. **Set up Supabase database**
   - Create a new Supabase project
   - Run the SQL schema from `database/schema.sql` in your Supabase SQL editor
   - Configure Row Level Security policies

5. **Start development server**
   ```bash
   npm run dev
   ```

6. **Open the application**
   Navigate to `http://localhost:3000` in your browser

## 📋 Implementation Status

### ✅ Completed Features

#### Phase 1: Landing Page
- [x] Hero section with gradient background
- [x] Features showcase for customers and merchants
- [x] How it works section with step-by-step guide
- [x] Partner marketplace integration
- [x] Statistics and social proof
- [x] Customer testimonials
- [x] Call-to-action sections
- [x] Footer with comprehensive links

#### Phase 2: Authentication System
- [x] Customer registration with form validation
- [x] Merchant registration flow
- [x] SMS verification system (mock implementation)
- [x] Login/logout functionality
- [x] Password reset flow
- [x] Role-based access control

#### Phase 3: Database Schema
- [x] Comprehensive database design
- [x] User and merchant tables
- [x] Purchase and payment tracking
- [x] Settlement and commission handling
- [x] AI chat logs and notifications
- [x] Audit trails and security
- [x] Row Level Security policies
- [x] Automated triggers and functions

#### Phase 4: Core Infrastructure
- [x] Supabase client configuration
- [x] Global state management with Zustand
- [x] Form validation with Zod
- [x] Utility functions and helpers
- [x] Custom CSS design system
- [x] TypeScript type definitions

### 🔄 In Progress / To Do

#### Phase 5: Customer Portal
- [ ] Customer dashboard with purchase overview
- [ ] Payment history and upcoming payments
- [ ] Payment processing (QR codes, transfers)
- [ ] AI financial advisor chat
- [ ] Profile management
- [ ] Notification center
- [ ] Payment method management

#### Phase 6: Merchant Portal
- [ ] Merchant dashboard with sales analytics
- [ ] Transaction management
- [ ] Settlement tracking
- [ ] Customer insights
- [ ] AI business advisor
- [ ] Bank account management
- [ ] Reporting tools

#### Phase 7: Admin Panel
- [ ] Merchant approval workflow
- [ ] User management interface
- [ ] Platform analytics and KPIs
- [ ] Payment dispute handling
- [ ] System configuration
- [ ] Audit logs viewer

#### Phase 8: Payment Processing
- [ ] Bancard QR code integration
- [ ] Bank transfer processing
- [ ] Card payment handling
- [ ] Payment verification system
- [ ] Automated settlement calculations
- [ ] Receipt generation

#### Phase 9: AI Integration
- [ ] Azure OpenAI API integration
- [ ] Customer support chatbot
- [ ] Merchant business insights
- [ ] Payment predictions
- [ ] Fraud detection

#### Phase 10: Advanced Features
- [ ] Email and SMS notifications
- [ ] PostHog analytics integration
- [ ] Mobile app preparation
- [ ] Performance optimization
- [ ] Security enhancements

## 🎨 Design System

The platform uses a custom design system based on the provided Figma reference:

### Colors
- **Primary**: Violet gradient (#8B5CF6 to #EC4899)
- **Secondary**: Pink to orange gradient (#EC4899 to #F97316)
- **Accent**: Various purple and pink shades
- **Neutral**: Gray scale for text and backgrounds

### Typography
- Clean, modern fonts with good readability
- Hierarchical sizing for headings and body text
- Proper contrast ratios for accessibility

### Components
- Custom button styles with hover effects
- Card components with subtle shadows
- Form inputs with focus states
- Responsive grid layouts
- Mobile-first design approach

## 📊 Database Schema

### Core Tables
- **users**: Customer profiles extending Supabase auth
- **merchants**: Business information and approval status
- **purchases**: Transaction records with installment details
- **payments**: Individual installment tracking
- **settlements**: Daily merchant payouts
- **notifications**: User notifications and alerts
- **ai_chat_logs**: AI conversation history
- **audit_logs**: System activity tracking

### Key Features
- Row Level Security (RLS) for data protection
- Automated triggers for business logic
- Comprehensive indexing for performance
- Foreign key relationships for data integrity
- Audit trails for compliance

## 🔐 Security Features

- **Row Level Security**: Database-level access control
- **JWT Authentication**: Secure token-based auth
- **Data Encryption**: All sensitive data encrypted
- **Input Validation**: Comprehensive form validation
- **CSRF Protection**: Cross-site request forgery prevention
- **Rate Limiting**: API abuse prevention
- **Audit Logging**: Complete activity tracking

## 📱 Responsive Design

The platform is fully responsive and works across:
- **Desktop**: Full-featured experience
- **Tablet**: Optimized layouts
- **Mobile**: Touch-friendly interfaces
- **Progressive Web App**: Offline capabilities

## 🧪 Testing

### Test Users
- **Admin**: <EMAIL>
- **Customer**: <EMAIL>
- **SMS Verification**: Use code `123456` for testing

### Test Scenarios
1. Customer registration and SMS verification
2. Merchant onboarding and approval
3. Purchase creation and payment processing
4. Settlement calculations and payouts
5. AI chat functionality
6. Admin approval workflows

## 🚀 Deployment

### Environment Setup
1. Set up production environment variables
2. Configure Supabase production instance
3. Set up Azure OpenAI production deployment
4. Configure payment provider credentials
5. Set up monitoring and analytics

### Deployment Steps
1. Build the application: `npm run build`
2. Deploy to Vercel, Netlify, or similar platform
3. Configure custom domain and SSL
4. Set up monitoring and error tracking
5. Configure backup and disaster recovery

## 📈 Analytics and Monitoring

### Key Metrics
- User registration and conversion rates
- Transaction volume and success rates
- Merchant onboarding and approval times
- Payment completion rates
- Customer satisfaction scores
- System performance metrics

### Tools
- **PostHog**: User behavior analytics
- **Supabase Analytics**: Database performance
- **Vercel Analytics**: Application performance
- **Custom Dashboards**: Business metrics

## 🤝 Contributing

### Development Guidelines
1. Follow TypeScript best practices
2. Use proper component structure
3. Implement proper error handling
4. Add comprehensive tests
5. Follow Git workflow conventions
6. Update documentation

### Code Style
- ESLint and Prettier configuration
- TypeScript strict mode
- Consistent naming conventions
- Proper component organization
- Comprehensive type definitions

## 📞 Support

### Documentation
- API documentation in `/docs/api`
- Component documentation in `/docs/components`
- Database schema in `/docs/database`
- Deployment guide in `/docs/deployment`

### Contact
- **Technical Support**: <EMAIL>
- **Business Inquiries**: <EMAIL>
- **Emergency**: +595 XXX XXX XXX

## 📄 License

This project is proprietary software owned by Alfa S.A. All rights reserved.

## 🎯 Next Steps

1. **Complete Customer Portal**: Implement dashboard and payment flows
2. **Build Merchant Portal**: Create analytics and management interface
3. **Develop Admin Panel**: Add merchant approval and system management
4. **Integrate Payment Processing**: Connect with Bancard and banking APIs
5. **Deploy MVP**: Launch with selected merchants and customers
6. **Gather Feedback**: Collect user feedback and iterate
7. **Scale Platform**: Expand to more merchants and customers

---

**Built with ❤️ in Paraguay**
