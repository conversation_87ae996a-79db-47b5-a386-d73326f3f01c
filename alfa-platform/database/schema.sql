-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Enable Row Level Security
ALTER DEFAULT PRIVILEGES REVOKE EXECUTE ON FUNCTIONS FROM PUBLIC;

-- Users table (extends Supabase Auth)
CREATE TABLE users (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT UNIQUE NOT NULL,
    full_name TEXT NOT NULL,
    phone TEXT UNIQUE NOT NULL,
    age INTEGER NOT NULL CHECK (age >= 18),
    address TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Merchants table
CREATE TABLE merchants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    business_name TEXT NOT NULL,
    ruc TEXT UNIQUE NOT NULL,
    business_type TEXT NOT NULL,
    average_ticket DECIMAL(10,2) NOT NULL,
    monthly_revenue DECIMAL(15,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected')),
    bank_account_number TEXT,
    bank_name TEXT,
    account_holder_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Purchases table
CREATE TABLE purchases (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    merchant_id UUID REFERENCES merchants(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    installments INTEGER NOT NULL DEFAULT 4 CHECK (installments BETWEEN 1 AND 4),
    description TEXT,
    status TEXT NOT NULL DEFAULT 'active' CHECK (status IN ('active', 'completed', 'defaulted')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payments table (installments)
CREATE TABLE payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    purchase_id UUID REFERENCES purchases(id) ON DELETE CASCADE,
    installment_number INTEGER NOT NULL CHECK (installment_number BETWEEN 1 AND 4),
    amount DECIMAL(10,2) NOT NULL,
    due_date DATE NOT NULL,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'overdue', 'verifying')),
    payment_method TEXT,
    payment_date TIMESTAMP WITH TIME ZONE,
    receipt_image_url TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(purchase_id, installment_number)
);

-- Settlements table (daily settlements to merchants)
CREATE TABLE settlements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    merchant_id UUID REFERENCES merchants(id) ON DELETE CASCADE,
    settlement_date DATE NOT NULL,
    total_amount DECIMAL(15,2) NOT NULL,
    transaction_count INTEGER NOT NULL DEFAULT 0,
    status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processed', 'failed')),
    bank_reference TEXT,
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(merchant_id, settlement_date)
);

-- Settlement items table (individual transactions in a settlement)
CREATE TABLE settlement_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    settlement_id UUID REFERENCES settlements(id) ON DELETE CASCADE,
    purchase_id UUID REFERENCES purchases(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    commission DECIMAL(10,2) NOT NULL DEFAULT 0,
    net_amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- AI Chat logs table
CREATE TABLE ai_chat_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    context TEXT NOT NULL CHECK (context IN ('customer', 'merchant', 'general')),
    user_message TEXT NOT NULL,
    ai_response TEXT NOT NULL,
    session_id UUID,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Notifications table
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('payment_reminder', 'payment_received', 'settlement', 'general')),
    title TEXT NOT NULL,
    message TEXT NOT NULL,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment methods table
CREATE TABLE payment_methods (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('debit_card', 'bank_account')),
    provider TEXT NOT NULL,
    last_four TEXT NOT NULL,
    is_default BOOLEAN DEFAULT FALSE,
    metadata JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Audit logs table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    action TEXT NOT NULL,
    table_name TEXT NOT NULL,
    record_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_phone ON users(phone);
CREATE INDEX idx_merchants_user_id ON merchants(user_id);
CREATE INDEX idx_merchants_status ON merchants(status);
CREATE INDEX idx_purchases_user_id ON purchases(user_id);
CREATE INDEX idx_purchases_merchant_id ON purchases(merchant_id);
CREATE INDEX idx_purchases_status ON purchases(status);
CREATE INDEX idx_payments_purchase_id ON payments(purchase_id);
CREATE INDEX idx_payments_due_date ON payments(due_date);
CREATE INDEX idx_payments_status ON payments(status);
CREATE INDEX idx_settlements_merchant_id ON settlements(merchant_id);
CREATE INDEX idx_settlements_date ON settlements(settlement_date);
CREATE INDEX idx_notifications_user_id ON notifications(user_id);
CREATE INDEX idx_notifications_read_at ON notifications(read_at);

-- Create functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_merchants_updated_at BEFORE UPDATE ON merchants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_purchases_updated_at BEFORE UPDATE ON purchases FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payments_updated_at BEFORE UPDATE ON payments FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_settlements_updated_at BEFORE UPDATE ON settlements FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_payment_methods_updated_at BEFORE UPDATE ON payment_methods FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE merchants ENABLE ROW LEVEL SECURITY;
ALTER TABLE purchases ENABLE ROW LEVEL SECURITY;
ALTER TABLE payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE settlements ENABLE ROW LEVEL SECURITY;
ALTER TABLE settlement_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE ai_chat_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_methods ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;

-- RLS Policies for Users
CREATE POLICY "Users can view their own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update their own profile" ON users FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert their own profile" ON users FOR INSERT WITH CHECK (auth.uid() = id);

-- RLS Policies for Merchants
CREATE POLICY "Merchants can view their own data" ON merchants FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Merchants can update their own data" ON merchants FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "Merchants can insert their own data" ON merchants FOR INSERT WITH CHECK (user_id = auth.uid());

-- RLS Policies for Purchases
CREATE POLICY "Users can view their own purchases" ON purchases FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Merchants can view their purchases" ON purchases FOR SELECT USING (merchant_id IN (SELECT id FROM merchants WHERE user_id = auth.uid()));
CREATE POLICY "Users can insert their own purchases" ON purchases FOR INSERT WITH CHECK (user_id = auth.uid());

-- RLS Policies for Payments
CREATE POLICY "Users can view their own payments" ON payments FOR SELECT USING (
    purchase_id IN (SELECT id FROM purchases WHERE user_id = auth.uid())
);
CREATE POLICY "Merchants can view their payments" ON payments FOR SELECT USING (
    purchase_id IN (SELECT id FROM purchases WHERE merchant_id IN (SELECT id FROM merchants WHERE user_id = auth.uid()))
);
CREATE POLICY "Users can update their own payments" ON payments FOR UPDATE USING (
    purchase_id IN (SELECT id FROM purchases WHERE user_id = auth.uid())
);

-- RLS Policies for Settlements
CREATE POLICY "Merchants can view their own settlements" ON settlements FOR SELECT USING (
    merchant_id IN (SELECT id FROM merchants WHERE user_id = auth.uid())
);

-- RLS Policies for AI Chat Logs
CREATE POLICY "Users can view their own chat logs" ON ai_chat_logs FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can insert their own chat logs" ON ai_chat_logs FOR INSERT WITH CHECK (user_id = auth.uid());

-- RLS Policies for Notifications
CREATE POLICY "Users can view their own notifications" ON notifications FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can update their own notifications" ON notifications FOR UPDATE USING (user_id = auth.uid());

-- RLS Policies for Payment Methods
CREATE POLICY "Users can view their own payment methods" ON payment_methods FOR SELECT USING (user_id = auth.uid());
CREATE POLICY "Users can insert their own payment methods" ON payment_methods FOR INSERT WITH CHECK (user_id = auth.uid());
CREATE POLICY "Users can update their own payment methods" ON payment_methods FOR UPDATE USING (user_id = auth.uid());
CREATE POLICY "Users can delete their own payment methods" ON payment_methods FOR DELETE USING (user_id = auth.uid());

-- Function to create payment schedule when a purchase is created
CREATE OR REPLACE FUNCTION create_payment_schedule()
RETURNS TRIGGER AS $$
DECLARE
    installment_amount DECIMAL(10,2);
    due_date DATE;
    i INTEGER;
BEGIN
    installment_amount := CEIL(NEW.amount / NEW.installments);
    
    FOR i IN 1..NEW.installments LOOP
        due_date := (NEW.created_at + INTERVAL '1 month' * (i - 1))::DATE;
        
        INSERT INTO payments (purchase_id, installment_number, amount, due_date)
        VALUES (NEW.id, i, installment_amount, due_date);
    END LOOP;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to create payment schedule
CREATE TRIGGER create_payment_schedule_trigger
    AFTER INSERT ON purchases
    FOR EACH ROW
    EXECUTE FUNCTION create_payment_schedule();

-- Function to update purchase status based on payments
CREATE OR REPLACE FUNCTION update_purchase_status()
RETURNS TRIGGER AS $$
DECLARE
    total_payments INTEGER;
    paid_payments INTEGER;
    overdue_payments INTEGER;
BEGIN
    SELECT COUNT(*), 
           SUM(CASE WHEN status = 'paid' THEN 1 ELSE 0 END),
           SUM(CASE WHEN status = 'overdue' THEN 1 ELSE 0 END)
    INTO total_payments, paid_payments, overdue_payments
    FROM payments 
    WHERE purchase_id = NEW.purchase_id;
    
    IF paid_payments = total_payments THEN
        UPDATE purchases SET status = 'completed' WHERE id = NEW.purchase_id;
    ELSIF overdue_payments > 0 THEN
        UPDATE purchases SET status = 'defaulted' WHERE id = NEW.purchase_id;
    END IF;
    
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to update purchase status
CREATE TRIGGER update_purchase_status_trigger
    AFTER UPDATE ON payments
    FOR EACH ROW
    EXECUTE FUNCTION update_purchase_status();

-- Function to mark overdue payments
CREATE OR REPLACE FUNCTION mark_overdue_payments()
RETURNS void AS $$
BEGIN
    UPDATE payments 
    SET status = 'overdue' 
    WHERE status = 'pending' 
    AND due_date < CURRENT_DATE;
END;
$$ language 'plpgsql';

-- Create admin user role
CREATE ROLE admin_user;
GRANT ALL ON ALL TABLES IN SCHEMA public TO admin_user;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO admin_user;

-- Insert sample data for testing
INSERT INTO users (id, email, full_name, phone, age, address) VALUES
('12345678-1234-1234-1234-123456789012', '<EMAIL>', 'Admin User', '981234567', 30, 'Asunción, Paraguay'),
('87654321-4321-4321-4321-210987654321', '<EMAIL>', 'Cliente Test', '987654321', 25, 'San Lorenzo, Paraguay');

INSERT INTO merchants (user_id, business_name, ruc, business_type, average_ticket, monthly_revenue, status) VALUES
('12345678-1234-1234-1234-123456789012', 'Tienda Alfa', '80123456-7', 'Retail', 50000, 5000000, 'approved'); 